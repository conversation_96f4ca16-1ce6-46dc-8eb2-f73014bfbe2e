//
//  OrderDetailViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import Foundation
import Combine

/// 订单详情ViewModel
class OrderDetailViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 订单详情数据
    @Published var orderDetail: OrderDetailModel?

    /// 申请退款成功消息
    @Published var refundSuccessMessage: String?

    /// 申请退款失败消息
    @Published var refundErrorMessage: String?

    // MARK: - Private Properties

    private var orderId: Int = 0

    // MARK: - 初始化

    init(orderId: Int) {
        self.orderId = orderId
        super.init()
    }

    // MARK: - 数据请求


    /// 获取订单详情
    func fetchOrderDetail() {
        let params = RequestParameters([
            "order_id": orderId
        ])

        requestModel(OrderService.orderDetail(params: params), type: OrderDetailResponse.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("加载订单详情失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                var order = OrderDetailModel()
                order = response.order
                order.status_map = response.status_map
                self?.orderDetail = order
            }
            .store(in: &cancellables)
    }
    ///取消订单
    func cancelOrder(){
        let params = RequestParameters([
            "order_id": orderId
        ])
        requestModel(OrderService.cancel(params: params), type: ResponseModel.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("取消订单失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                self?.refreshData()
            }
            .store(in: &cancellables)
    }

    /// 申请退款
    /// - Parameters:
    func applyRefund(params:[String:Any]) {
        let params = RequestParameters(params)
        requestModel(OrderService.applyRefund(params: params), type: ResponseModel.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.refundErrorMessage = error.localizedDescription
                    print("申请退款失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                self?.refundSuccessMessage = response.message
                print("申请退款成功: \(response.message)")
                // 刷新订单详情数据
                self?.refreshData()
            }
            .store(in: &cancellables)
    }

    /// 卖家同意退款申请
    /// - Parameters:
    ///   - addressId: 退货地址ID（仅退款类型=2时可传）
    ///   - addressSnapshot: 退货地址快照（仅退款类型=2时必传）
    func agreeRefund(addressId: Int? = nil, addressSnapshot: String? = nil) {
        var params: [String: Any] = [
            "order_id": orderId
        ]

        // 如果有地址ID，添加到参数中
        if let addressId = addressId {
            params["address_id"] = addressId
        }

        // 如果有地址快照，添加到参数中
        if let addressSnapshot = addressSnapshot {
            params["address_snapshot"] = addressSnapshot
        }

        let requestParams = RequestParameters(params)
        requestModel(OrderService.agreeRefund(params: requestParams), type: ResponseModel.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.refundErrorMessage = error.localizedDescription
                    print("同意退款失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                self?.refundSuccessMessage = response.message
                print("同意退款成功: \(response.message)")
                // 刷新订单详情数据
                self?.refreshData()
            }
            .store(in: &cancellables)
    }
    override func refreshData() {
        fetchOrderDetail()
    }

    /// 复制文本到剪贴板
    /// - Parameter text: 要复制的文本
    func copyToClipboard(text: String) {
        UIPasteboard.general.string = text
        // TODO: 显示复制成功提示
        print("已复制: \(text)")
    }
    
    // MARK: - Private Methods

    deinit {
        // 清理资源
    }
}
